"""
Test script to verify the cookie manager AttributeError fix
验证cookie manager AttributeError修复的测试脚本
"""

import asyncio
import sys
import os

# Add server directory to path
sys.path.append(os.path.dirname(__file__))

from logger import logger


async def test_cookie_manager_initialization():
    """Test that cookie manager is properly initialized"""
    print("=== Testing Cookie Manager Initialization ===")
    
    try:
        from unified_data_scheduler import UnifiedDataScheduler
        
        # Test creating instance
        scheduler = UnifiedDataScheduler(data_types=['user'], uid="401315430")
        print(f"✓ Scheduler created successfully")
        
        # Check if cookie_manager attribute exists
        if hasattr(scheduler, 'cookie_manager'):
            print(f"✓ cookie_manager attribute exists: {scheduler.cookie_manager}")
        else:
            print("✗ cookie_manager attribute missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Initialization test failed: {e}")
        return False


async def test_initialize_method():
    """Test that initialize method works without AttributeError"""
    print("\n=== Testing Initialize Method ===")
    
    try:
        from unified_data_scheduler import UnifiedDataScheduler
        
        scheduler = UnifiedDataScheduler(data_types=['user'], uid="401315430")
        
        # This should not raise AttributeError
        await scheduler.initialize()
        print("✓ Initialize method completed without AttributeError")
        
        return True
        
    except AttributeError as e:
        if 'cookie_manager' in str(e):
            print(f"✗ AttributeError still exists: {e}")
            return False
        else:
            print(f"✗ Different AttributeError: {e}")
            return False
    except Exception as e:
        print(f"⚠ Initialize failed with different error (expected): {e}")
        return True  # Other errors are expected due to missing dependencies


async def test_once_mode_execution():
    """Test the once mode execution that was failing"""
    print("\n=== Testing Once Mode Execution ===")
    
    try:
        from unified_scheduler_main import run_once
        
        # This should not raise AttributeError
        await run_once(data_types=['user'], uid="401315430")
        print("✓ Once mode execution completed without AttributeError")
        
        return True
        
    except AttributeError as e:
        if 'cookie_manager' in str(e):
            print(f"✗ AttributeError still exists: {e}")
            return False
        else:
            print(f"✗ Different AttributeError: {e}")
            return False
    except Exception as e:
        print(f"⚠ Once mode failed with different error (expected): {e}")
        return True  # Other errors are expected due to missing dependencies


async def run_fix_verification_tests():
    """Run all fix verification tests"""
    print("Starting Cookie Manager AttributeError Fix Verification...")
    print("=" * 70)
    
    tests = [
        ("Cookie Manager Initialization", test_cookie_manager_initialization),
        ("Initialize Method", test_initialize_method),
        ("Once Mode Execution", test_once_mode_execution),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("Fix Verification Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The AttributeError has been successfully fixed.")
        print("\n✅ Fix Summary:")
        print("- Added optional cookie manager import with proper error handling")
        print("- Initialized cookie_manager attribute in __init__ method")
        print("- Made cookie manager functionality optional in initialize method")
        print("- The original error 'UnifiedDataScheduler' object has no attribute 'cookie_manager' is resolved")
    elif passed >= len(results) * 0.7:
        print("⚠ Most tests passed. The fix is mostly working.")
    else:
        print("❌ Many tests failed. The fix may not be complete.")
    
    return passed == len(results)


if __name__ == "__main__":
    try:
        result = asyncio.run(run_fix_verification_tests())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
